import clsx from 'clsx';
import Link from 'next/link';

interface Props {
    activeTab: string;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    setActiveTab: any;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    tabs: any[];
}

export const Tabs = ({ activeTab, setActiveTab, tabs }: Props) => {
    return (
        <div className="flex flex-row gap-4">
            {tabs.map(item => {
                if (item.hidden) return null;

                if (item.key) {
                    return (
                        <button
                            onClick={() => setActiveTab(item.key)}
                            key={item.key}
                            className={clsx(
                                'bg-main-1300/50 text-main-600 rounded-t-lg py-1 px-4 text-sm',
                                activeTab === item.key
                                    ? 'border-transparent'
                                    : 'border-b border-main-600/20 text-main-600/40'
                            )}
                        >
                            {item.label}
                        </button>
                    );
                }
                if (item.href) {
                    return (
                        <Link
                            key={item.key}
                            href={item.href}
                            target="_blank"
                            className={clsx(
                                'bg-main-1300/50 text-main-600 rounded-t-lg py-1 px-4 text-sm',
                                activeTab === item.key
                                    ? 'border-transparent'
                                    : 'border-b border-main-600/20 text-main-600/40'
                            )}
                        >
                            {item.label}
                        </Link>
                    );
                }
                if (item.onClick) {
                    return (
                        <button
                            onClick={item.onClick}
                            key={`item-${item.label}-tab`}
                            className={clsx(
                                'bg-main-1300/50 text-main-600 rounded-t-lg py-1 px-4 text-sm',
                                activeTab === item.key
                                    ? 'border-transparent'
                                    : 'border-b border-main-600/20 text-main-600/40'
                            )}
                        >
                            {item.label}
                        </button>
                    );
                }
            })}
        </div>
    );
};
