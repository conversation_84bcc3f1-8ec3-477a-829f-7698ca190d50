'use client';
import { LogoutButton } from '../LogoutButton';
import Image from 'next/image';
import { RouterPaths } from '@/common/routerPaths';
import Link from 'next/link';
import { useUserStore } from '@/store/userStore';

const MAIN_ITEMS = [
    {
        icon: '/assets/icon-House.svg',
        label: 'Home',
        href: RouterPaths.DASHBOARD,
    },
    {
        icon: '/assets/icon-Certificate.svg',
        protect: true,
        label: 'New Issuer',
        href: RouterPaths.NEW_ISSUER,
    },
    {
        icon: '/assets/icon-ShieldStar.svg',
        protect: true,
        label: 'New Verifier',
        href: RouterPaths.NEW_VERIFIER,
    },
];

const FOOTER_ITEMS = [
    {
        icon: '/assets/icon-User.svg',
        label: 'Profile',
        href: RouterPaths.PROFILE,
    },
    {
        icon: '/assets/icon-FileWrite.svg',
        label: 'Terms & Conditions',
        href: RouterPaths.TERMS_AND_CONDITIONS,
    },
];

export const SideBar = () => {
    const { user } = useUserStore();

    return (
        <div className="h-screen fixed left-0 top-0 z-10 py-8 px-4 w-52 flex flex-col justify-between bg-main-1300/50">
            <div className="gap-2 flex flex-col">
                <Image
                    src="/assets/LogoNav.png"
                    alt="Empe One Click Deployer Logo"
                    width={400}
                    height={100}
                    className="w-full max-w-[600px] pb-10 px-4 self-center"
                />

                {MAIN_ITEMS.map(item => {
                    if (item.protect && user?.subscriptionActive === false) return null;
                    return (
                        <Link
                            key={item.href}
                            href={item.href}
                            className="flex items-center text-main-600 gap-4 p-4 hover:bg-white/10 rounded-lg transition-all duration-300 ease-in-out"
                        >
                            <Image src={item.icon} alt="" width={24} height={24} />
                            {item.label}
                        </Link>
                    );
                })}
            </div>
            <div>
                {FOOTER_ITEMS.map(item => (
                    <Link
                        key={item.href}
                        href={item.href}
                        className="flex items-center text-main-600 gap-4 p-4 hover:bg-white/10 rounded-lg transition-all duration-300 ease-in-out"
                    >
                        <Image src={item.icon} alt="" width={24} height={24} />
                        {item.label}
                    </Link>
                ))}

                <LogoutButton />
            </div>
        </div>
    );
};
